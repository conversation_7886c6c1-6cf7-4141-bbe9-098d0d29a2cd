import PaymentLogos from "../PaymentLogos";
import SecurePayFields from "../SecurePayFields";
import type { PayFieldsConfig, BillingAddress, PaymentInfo } from "../../../types/payment";
import { PaymentValidation } from "./PaymentValidation";

interface PaymentFormSectionProps {
  payFieldsConfig: PayFieldsConfig | null;
  paymentInfo: PaymentInfo | null;
  billingAddress: BillingAddress;
  error: string | null;
  isAddressValid: boolean;
  loading?: boolean;
  onSuccess: (response: unknown) => void;
  onFailure: (error: unknown) => void;
}

export const PaymentFormSection = ({
  payFieldsConfig,
  paymentInfo,
  billingAddress,
  error,
  isAddressValid,
  loading = false,
  onSuccess,
  onFailure,
}: PaymentFormSectionProps) => {
  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-4">Payment Information</h2>

      {/* Accepted cards */}
      <div className="mb-4 flex flex-col">
        <p className="text-sm text-gray-600 mb-2">Accepted Payment Methods</p>
        <PaymentLogos showGooglePay={payFieldsConfig?.googlePayConfig?.enabled || false} />
      </div>

      <div className="mb-4">
        <PaymentValidation error={error} isAddressValid={isAddressValid} />
      </div>

      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin h-8 w-8 border-4 border-t-transparent border-blue-600 rounded-full mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading payment methods...</p>
        </div>
      ) : payFieldsConfig ? (
        <div className={!isAddressValid ? "opacity-50 pointer-events-none" : ""}>
          <SecurePayFields
            config={payFieldsConfig}
            paymentInfo={paymentInfo}
            onSuccess={onSuccess}
            onFailure={onFailure}
            billingAddress={billingAddress}
          />
        </div>
      ) : null}

      {/* Save card option */}
      <div className="mt-4">
        <label className="flex items-center text-sm text-gray-600">
          <input type="checkbox" className="mr-2 h-4 w-4 text-[#364F6B] border-gray-300 rounded focus:ring-[#364F6B]" />
          Save card for future payments
        </label>
      </div>

      {/* Payment note */}
      <div className="mt-4 text-xs text-gray-500 text-center">
        <p>Payment processed securely. Choose your preferred payment method and complete the transaction.</p>
      </div>
    </div>
  );
};
