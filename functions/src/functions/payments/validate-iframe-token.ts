import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { createIframeResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";
import { validateMerchantById } from "../../service/payrix.service.js";
import { validateToken } from "./generate-integration-token";
import { withIframeSecurity, validateTokenFormat } from "../../middleware/security.js";
import { z } from "zod";

// HTTP Status Codes
const HTTP_STATUS = {
  OK: 200,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  NOT_FOUND: 404,
  INTERNAL_ERROR: 500,
} as const;

// Payment Configuration Constants
const PAYMENT_CONFIG = {
  DEFAULT_MODE: "token" as const,
  DEFAULT_TXN_TYPE: "auth" as const,
  DEFAULT_PUBLIC_KEY: "default-public-key",
  DEFAULT_COUNTRY: "US",
  DEFAULT_MERCHANT_NAME: "Unknown",
} as const;

// Error and Success Messages
const MESSAGES = {
  BODY_REQUIRED: "Request body is required",
  BODY_REQUIRED_DETAIL: "Please provide a token",
  VALIDATION_FAILED: "Validation failed",
  INVALID_REQUEST: "Invalid request data",
  INVALID_TOKEN_FORMAT: "Invalid token format",
  TOKEN_VALIDATION_FAILED: "Token validation failed",
  INVALID_OR_EXPIRED_TOKEN: "Invalid or expired token",
  MERCHANT_VALIDATION_FAILED: "Merchant validation failed",
  INVALID_OR_INACTIVE_MERCHANT: "Invalid or inactive merchant",
  INTERNAL_ERROR: "Internal server error",
  PROCESSING_FAILED: "Failed to validate token and generate payment configuration",
  UNKNOWN_ERROR: "Unknown error",
  SUCCESS_MESSAGE: "Token validated and payment configuration generated successfully",
} as const;

interface TokenValidationRequest {
  token: string;
  paymentMethod?: "card" | "google_pay";
}

interface GooglePayConfig {
  enabled?: boolean;
  merchantName?: string;
  environment?: "TEST" | "PRODUCTION";
  allowedCardNetworks?: Array<"VISA" | "MASTERCARD" | "AMEX" | "DISCOVER" | "JCB" | "INTERAC">;
  allowedCardAuthMethods?: Array<"PAN_ONLY" | "CRYPTOGRAM_3DS">;
  billingAddressRequired?: boolean;
  shippingAddressRequired?: boolean;
  phoneNumberRequired?: boolean;
}

interface PayFieldsConfig {
  merchantId: string;
  publicKey: string;
  amount: number;
  description: string;
  mode: "txn" | "txnToken" | "token";
  txnType: "sale" | "auth" | "ecsale";
  googlePayConfig?: GooglePayConfig;
  enableDigitalWallets?: boolean;
}

interface MerchantInfo {
  id: string;
  name: string;
  status: number;
  address?: {
    line1?: string;
    line2?: string;
    city?: string;
    state?: string;
    zip?: string;
    country?: string;
  };
  contactEmail?: string;
  contactPhone?: string;
}

const tokenValidationSchema = z.object({
  token: z.string().min(1, "Token is required"),
  paymentMethod: z.enum(["card", "google_pay"]).optional(),
});

const handlerImpl = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (!event.body) {
      return createIframeResponse(HTTP_STATUS.BAD_REQUEST, {
        error: MESSAGES.BODY_REQUIRED,
        message: MESSAGES.BODY_REQUIRED_DETAIL,
      });
    }

    let requestData: TokenValidationRequest;
    try {
      requestData = tokenValidationSchema.parse(JSON.parse(event.body));
    } catch (error) {
      if (error instanceof z.ZodError) {
        return createIframeResponse(HTTP_STATUS.BAD_REQUEST, {
          error: MESSAGES.VALIDATION_FAILED,
          message: MESSAGES.INVALID_REQUEST,
          details: error.errors,
        });
      }
      throw error;
    }

    const { token, paymentMethod = "card" } = requestData;
    const tokenFormatValidation = validateTokenFormat(token);
    if (!tokenFormatValidation.isValid) {
      return createIframeResponse(tokenFormatValidation.statusCode || HTTP_STATUS.BAD_REQUEST, {
        error: tokenFormatValidation.error,
        message: MESSAGES.INVALID_TOKEN_FORMAT,
      });
    }

    const tokenValidation = await validateToken(token);

    if (!tokenValidation.isValid || !tokenValidation.data) {
      return createIframeResponse(HTTP_STATUS.UNAUTHORIZED, {
        error: MESSAGES.TOKEN_VALIDATION_FAILED,
        message: tokenValidation.error || MESSAGES.INVALID_OR_EXPIRED_TOKEN,
      });
    }

    const {
      merchantId,
      description,
      amount,
      returnUrl,
      currency,
      items,
      taxAmount,
      shippingAmount,
      dutyAmount,
      orderNumber,
      invoiceNumber,
      customerCode,
      orderDiscount,
      googlePayConfig,
      enableDigitalWallets,
      merchantInfo: tokenMerchantInfo,
    } = tokenValidation.data;

    const merchantValidation = await validateMerchantById(merchantId);

    if (!merchantValidation.isValid) {
      return createIframeResponse(HTTP_STATUS.NOT_FOUND, {
        error: MESSAGES.MERCHANT_VALIDATION_FAILED,
        message: merchantValidation.error || MESSAGES.INVALID_OR_INACTIVE_MERCHANT,
        details: {
          merchantId,
          validationError: merchantValidation.error,
        },
      });
    }

    // Both card and Google Pay should use token mode for backend processing
    // Token mode generates a payment token that our backend can process
    const configAmount = amount || 0;
    const isGooglePay = paymentMethod === "google_pay";
    
    const config: PayFieldsConfig = {
      merchantId,
      publicKey: process.env.PAYRIX_PUBLIC_API_KEY || PAYMENT_CONFIG.DEFAULT_PUBLIC_KEY,
      amount: configAmount,
      description,
      mode: PAYMENT_CONFIG.DEFAULT_MODE,
      txnType: PAYMENT_CONFIG.DEFAULT_TXN_TYPE,
      googlePayConfig: isGooglePay ? (googlePayConfig as GooglePayConfig | undefined) : undefined,
      enableDigitalWallets: isGooglePay ? enableDigitalWallets : false,
    };



    const merchant = merchantValidation.merchant as Record<string, unknown> | undefined;
    const merchantAddress = merchant?.address as Record<string, unknown> | undefined;

    // Extract merchant name with clearer logic
    const merchantName = merchant?.dba as string || merchant?.name as string || PAYMENT_CONFIG.DEFAULT_MERCHANT_NAME;
    const merchantStatus = (merchant?.status as number) || 0;
    
    // Build merchant address if available
    const builtMerchantAddress = merchantAddress ? {
      line1: merchantAddress.line1 as string,
      line2: merchantAddress.line2 as string,
      city: merchantAddress.city as string,
      state: merchantAddress.state as string,
      zip: merchantAddress.zip as string,
      country: (merchantAddress.country as string) || PAYMENT_CONFIG.DEFAULT_COUNTRY,
    } : undefined;
    
    // Use token merchant info if available, otherwise use built address
    const finalAddress = tokenMerchantInfo?.address || builtMerchantAddress;
    const contactEmail = tokenMerchantInfo?.contactEmail || (merchant?.email as string);
    const contactPhone = tokenMerchantInfo?.contactPhone || (merchant?.phone as string);

    const merchantInfo: MerchantInfo = {
      id: merchantId,
      name: merchantName,
      status: merchantStatus,
      address: finalAddress,
      contactEmail,
      contactPhone,
    };

    return createIframeResponse(HTTP_STATUS.OK, {
      success: true,
      message: MESSAGES.SUCCESS_MESSAGE,
      data: {
        config,
        merchantInfo,
        paymentInfo: {
          description,
          amount,
          currency,
          returnUrl,
          items,
          taxAmount,
          shippingAmount,
          dutyAmount,
          orderNumber,
          invoiceNumber,
          customerCode,
          orderDiscount,
        },
      },
    });
  } catch (error) {
    logger.error("Error validating iframe token", { error });

    return createIframeResponse(HTTP_STATUS.INTERNAL_ERROR, {
      error: MESSAGES.INTERNAL_ERROR,
      message: MESSAGES.PROCESSING_FAILED,
      details: error instanceof Error ? error.message : MESSAGES.UNKNOWN_ERROR,
    });
  }
};

export const handler = withIframeSecurity(handlerImpl);
